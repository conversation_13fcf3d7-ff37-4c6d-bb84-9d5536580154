import {
  BelongsToMany,
  Table,
  Column,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  DataType,
} from 'sequelize-typescript';

import { ProjectUser } from '../projects/project-user.entity';
import { Project } from '../projects/project.entity';

export interface UserCreationAttrs {
  clerkId: string;
  username: string | null;
}

@Table({
  tableName: 'users',
  timestamps: true,
})
export class User extends Model<User, UserCreationAttrs> {
  @PrimaryKey
  @Default(DataType.UUIDV4)
  @Column({ type: DataType.UUID })
  declare id: string;

  @Column({ allowNull: false, unique: true, field: 'clerk_id' })
  declare clerkId: string;

  @Column({ allowNull: true })
  declare username: string;

  @Column({ field: 'created_at' })
  declare readonly createdAt: Date;

  @Column({ field: 'updated_at' })
  declare readonly updatedAt: Date;

  @BelongsToMany(() => Project, () => ProjectUser)
  declare projects?: Project[];
}
