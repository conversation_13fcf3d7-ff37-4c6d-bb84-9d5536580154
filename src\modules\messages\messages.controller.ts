import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';

import { CreateMessageDto } from './dto/create-message.dto';
import { MessageTypes } from './message.constants';
import { MessagesService } from './messages.service';
import { SwaggerCreateMessage, SwaggerGetMessages } from './messages.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';
import { CursorPaginationQueryDto } from '../../common/dto/cursor-pagination.dto';
import { ProjectOwnershipGuard } from '../../common/guards/project-ownership.guard';
import { errorResponse, successResponse } from '../../common/utils/response.util';
import { User } from '../users/users.entity';

@ApiBearerAuth('clerk-auth')
@Controller('projects')
export class MessagesController {
  constructor(private readonly messagesService: MessagesService) {}

  @SwaggerCreateMessage()
  @UseGuards(ProjectOwnershipGuard)
  @Post(':projectId/versions/:versionId/messages')
  async createMessage(
    @DbAuthUser() user: User,
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
    @Body() dto: CreateMessageDto,
  ) {
    try {
      console.log('qwe');
      dto.type = MessageTypes.User;
      const messages = await this.messagesService.askNStoreAI(user.id, projectId, versionId, dto);

      return successResponse(messages, 201);
    } catch (e) {
      return errorResponse(e, 500);
    }
  }

  @SwaggerGetMessages()
  @Get(':projectId/versions/:versionId/messages')
  async getVersionMessages(
    @Param('projectId') projectId: string,
    @Param('versionId') versionId: string,
    @Query() query: CursorPaginationQueryDto,
  ) {
    const result = await this.messagesService.getPaginatedMessages({ projectId, versionId, query });
    return successResponse(result);
  }

  @SwaggerGetMessages()
  @Get(':projectId/messages')
  async getProjectMessages(
    @Param('projectId') projectId: string,
    @Query() query: CursorPaginationQueryDto,
  ) {
    const result = await this.messagesService.getPaginatedMessages({ projectId, query });
    return successResponse(result);
  }
}
