import { Injectable } from '@nestjs/common';
import { Sequelize } from 'sequelize-typescript';

import { GetStatusDtoResponse } from './dto/get-status.dto';

@Injectable()
export class StatusService {
  constructor(private readonly sequelize: Sequelize) {}

  async getStatus(): Promise<GetStatusDtoResponse> {
    const dbStatus = await this.checkDbConnection();

    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      database: dbStatus,
    };
  }

  private async checkDbConnection(): Promise<'connected' | 'disconnected'> {
    try {
      await this.sequelize.authenticate();
      return 'connected';
    } catch {
      return 'disconnected';
    }
  }
}
