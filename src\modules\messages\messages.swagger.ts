import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';
import {
  CursorPaginationMetaDto,
  CursorPaginationResponseDto,
} from 'src/common/dto/cursor-pagination.dto';

import { MessageResponseDto } from './dto/create-message.dto';
import { GetMessageDto } from './dto/get-message.dto';
import { ResponseWrapperDto } from '../../common/dto/response-wrapper.dto';

export function SwaggerCreateMessage() {
  return applyDecorators(
    ApiExtraModels(ResponseWrapperDto, MessageResponseDto),
    ApiOkResponse({
      description: 'Create a new message within a specific project version.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(MessageResponseDto) },
            },
          },
        ],
      },
    }),
  );
}

export const SwaggerGetMessages = () =>
  applyDecorators(
    ApiExtraModels(CursorPaginationResponseDto, CursorPaginationMetaDto, GetMessageDto),
    ApiOkResponse({
      description: 'Returns a paginated list of messages',
      schema: {
        allOf: [
          { $ref: getSchemaPath(CursorPaginationResponseDto) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(GetMessageDto) },
              },
              meta: { $ref: getSchemaPath(CursorPaginationMetaDto) },
            },
          },
        ],
      },
    }),
  );
