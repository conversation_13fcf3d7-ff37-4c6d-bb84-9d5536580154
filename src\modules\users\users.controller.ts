import { Controller, Get } from '@nestjs/common';
import { ApiBearerAuth } from '@nestjs/swagger';
import { successResponse } from 'src/common/utils/response.util';

import { User } from './users.entity';
import { UsersService } from './users.service';
import { SwaggerGetCurrentUser } from './users.swagger';
import { DbAuthUser } from '../../common/decorators/db-auth-user.decorator';

@ApiBearerAuth('clerk-auth')
@Controller('users')
export class UsersController {
  constructor(private readonly usersService: UsersService) {}

  @SwaggerGetCurrentUser()
  @Get('me')
  getMe(@DbAuthUser() dbUser: User) {
    const data = {
      clerkId: dbUser.clerkId,
      username: dbUser.username,
    };

    return successResponse(data, 200);
  }
}
