import { ApiProperty } from '@nestjs/swagger';

export class CreateProjectDto {
  @ApiProperty()
  declare name: string;
}

export class CreateProjectDtoResponse {
  @ApiProperty()
  declare id: string;

  @ApiProperty()
  declare name: string;

  @ApiProperty()
  declare repositoryUrl: string;

  @ApiProperty({ nullable: true })
  declare publish_url: string | null;

  @ApiProperty()
  declare activeVersionId: string | null;

  @ApiProperty()
  declare createdAt: Date;

  @ApiProperty()
  declare updatedAt: Date;
}
