'use strict';

/** @type {import('sequelize-cli').Migration} */
module.exports = {
  async up(queryInterface, Sequelize) {
    await queryInterface.createTable('project_versions', {
      id: {
        type: Sequelize.UUID,
        primaryKey: true,
        defaultValue: Sequelize.literal('(UUID())'),
      },
      project_id: {
        type: Sequelize.UUID,
        allowNull: false,
        references: {
          model: 'projects',
          key: 'id',
        },
        onDelete: 'CASCADE',
      },
      version_number: {
        type: Sequelize.INTEGER,
        allowNull: false,
      },
      version_prompt: {
        type: Sequelize.DataTypes.TEXT('long'),
        allowNull: true,
      },
      version_branch: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      version_status: {
        type: Sequelize.STRING,
        allowNull: false,
      },
      feedback_info: {
        type: Sequelize.TEXT,
        allowNull: true,
      },
      feedback_score: {
        type: Sequelize.INTEGER,
        allowNull: true,
      },
      created_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP'),
      },
      updated_at: {
        type: Sequelize.DATE,
        allowNull: false,
        defaultValue: Sequelize.literal('CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP'),
      },
    });

    await queryInterface.addConstraint('project_versions', {
      fields: ['project_id', 'version_number'],
      type: 'unique',
      name: 'unique_project_version',
    });
  },

  async down(queryInterface) {
    await queryInterface.dropTable('project_versions');
  },
};
