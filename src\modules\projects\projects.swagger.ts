import { applyDecorators } from '@nestjs/common';
import { ApiExtraModels, ApiOkResponse, getSchemaPath } from '@nestjs/swagger';

import { CreateProjectVersionDtoResponse } from './dto/create-project-version.dto';
import { CreateProjectDtoResponse } from './dto/create-project.dto';
import { GetProjectDtoResponse } from './dto/get-project.dto';
import { Project } from './project.entity';
import {
  CursorPaginationMetaDto,
  CursorPaginationResponseDto,
} from '../../common/dto/cursor-pagination.dto';
import { ResponseWrapperDto } from '../../common/dto/response-wrapper.dto';

export const SwaggerGetProjects = () =>
  applyDecorators(
    ApiExtraModels(CursorPaginationResponseDto, CursorPaginationMetaDto, Project),
    ApiOkResponse({
      description: 'Returns a paginated list of projects belonging to the authenticated user.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(CursorPaginationResponseDto) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(Project) },
              },
              meta: { $ref: getSchemaPath(CursorPaginationMetaDto) },
            },
          },
        ],
      },
    }),
  );

export const SwaggerGetProjectById = () =>
  applyDecorators(
    ApiExtraModels(ResponseWrapperDto, GetProjectDtoResponse),
    ApiOkResponse({
      description: 'Returns details of a single project by its ID.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(GetProjectDtoResponse) },
            },
          },
        ],
      },
    }),
  );

export const SwaggerCreateProject = () =>
  applyDecorators(
    ApiExtraModels(ResponseWrapperDto, CreateProjectDtoResponse),
    ApiOkResponse({
      description: 'Creates a new project and returns the created project.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(CreateProjectDtoResponse) },
            },
          },
        ],
      },
    }),
  );

export const SwaggerUpdateProject = () =>
  applyDecorators(
    ApiExtraModels(ResponseWrapperDto, CreateProjectDtoResponse),
    ApiOkResponse({
      description: 'Updates an existing project and returns the updated project.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(CreateProjectDtoResponse) },
            },
          },
        ],
      },
    }),
  );

export const SwaggerCreateProjectVersion = () =>
  applyDecorators(
    ApiExtraModels(ResponseWrapperDto, CreateProjectVersionDtoResponse),
    ApiOkResponse({
      description: 'Creates a new version for a project and returns the created version.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(CreateProjectVersionDtoResponse) },
            },
          },
        ],
      },
    }),
  );

export const SwaggerUpdateProjectVersion = () =>
  applyDecorators(
    ApiExtraModels(ResponseWrapperDto, CreateProjectVersionDtoResponse),
    ApiOkResponse({
      description: 'Updates an existing project version and returns the updated version.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(CreateProjectVersionDtoResponse) },
            },
          },
        ],
      },
    }),
  );

export const SwaggerExecuteProjectVersion = () =>
  applyDecorators(
    ApiExtraModels(ResponseWrapperDto, CreateProjectVersionDtoResponse),
    ApiOkResponse({
      description:
        'Generates version prompt by aggregating all messages tied to the project version.',
      schema: {
        allOf: [
          { $ref: getSchemaPath(ResponseWrapperDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(CreateProjectVersionDtoResponse) },
            },
          },
        ],
      },
    }),
  );
