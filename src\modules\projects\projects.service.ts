import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';

import { CreateProjectVersionDto, UpdateProjectVersionDto } from './dto/create-project-version.dto';
import { CreateProjectDto } from './dto/create-project.dto';
import { GetProjectDto } from './dto/get-project.dto';
import { ProjectUser } from './project-user.entity';
import { ProjectVersion } from './project-version.entity';
import { ProjectUserRole } from './project.constants';
import { Project } from './project.entity';
import { paginateCursor } from '../../common/utils/paginate-cursor.util';
import { MessagesService } from '../messages/messages.service';
import { UpdateProjectDto } from './dto/update-project.dto';
import { GithubService } from '../../common/services/github/github.service';

@Injectable()
export class ProjectsService {
  constructor(
    @InjectModel(Project) private projectModel: typeof Project,
    @InjectModel(ProjectVersion) private versionModel: typeof ProjectVersion,
    @InjectModel(ProjectUser) private projectUserModel: typeof ProjectUser,
    @Inject(forwardRef(() => MessagesService))
    private readonly messagesService: MessagesService,
    private readonly gitHubService: GithubService,
  ) {}

  async createProject(dto: CreateProjectDto, userId: string) {
    const repositoryUrl = 'http://github.com';

    const project = await this.projectModel.create({ ...dto, repositoryUrl }, { returning: true });

    await this.projectUserModel.create({
      user_id: userId,
      project_id: project.id,
      role: ProjectUserRole.Owner,
    });

    const repository = await this.gitHubService.createRepository(project.id);
    project.repositoryUrl = repository.html_url;
    project.repositoryOwner = repository.owner.login;
    project.repositoryName = repository.name;
    await project.save();

    const version = await this.createProjectVersion({ version_prompt: '', project_id: project.id });

    await this.messagesService.initAIDialogue(userId, project.id, version.id);

    return await this.projectModel.findByPk(project.id, {
      include: ['activeVersion'],
    });
  }

  async createProjectVersion(dto: CreateProjectVersionDto & { project_id: string }) {
    const project = await this.projectModel.findByPk(dto.project_id);
    if (!project) throw new Error('Project not found');

    const lastVersion = await this.versionModel.findOne({
      where: { project_id: dto.project_id },
      order: [['createdAt', 'DESC']],
    });

    const versionNumber = lastVersion ? lastVersion.version_number + 1 : 1;
    const versionBranch = `version_${versionNumber}`;

    await this.gitHubService.createBranch(
      project.repositoryOwner,
      project.repositoryName,
      versionBranch,
    );

    const version = await this.versionModel.create(
      {
        ...dto,
        version_number: versionNumber,
        version_branch: versionBranch,
      },
      { returning: true },
    );

    await this.projectModel.update(
      { activeVersionId: version.id },
      { where: { id: dto.project_id } },
    );

    return version;
  }

  async getProject(dto: GetProjectDto) {
    return await this.projectModel.findByPk(dto.id, {
      include: ['versions'],
    });
  }

  async getProjectVersion(projectId: string, projectVersionId: string) {
    const version = await this.versionModel.findOne({
      where: {
        id: projectVersionId,
        project_id: projectId,
      },
    });

    if (!version) throw new Error('Project version not found');
    return version;
  }

  async getUserProjects(params: { userId: string; limit: number; cursor: string | undefined }) {
    const { userId, limit, cursor } = params;

    return await paginateCursor(Project, {
      limit,
      cursor,
      include: [
        {
          model: ProjectUser,
          where: { user_id: userId },
          attributes: [],
        },
      ],
      orderBy: 'created_at',
      orderDirection: 'ASC',
    });
  }

  async updateProject(id: string, dto: Partial<UpdateProjectDto>) {
    const project = await this.projectModel.findByPk(id);
    if (!project) {
      throw new Error('Project not found');
    }

    if (dto.activeVersionId) {
      const version = await this.versionModel.findOne({
        where: { id: dto.activeVersionId, project_id: id },
      });

      if (!version) {
        throw new Error('activeVersionId is invalid or does not belong to the project');
      }
    }

    await project.update(dto);
    return project;
  }

  async updateProjectVersion(projectId: string, versionId: string, dto: UpdateProjectVersionDto) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });

    if (!version) throw new Error('Project version not found');

    await version.update(dto);
    return version;
  }

  async executeVersionPrompt(projectId: string, versionId: string) {
    const version = await this.versionModel.findOne({
      where: { id: versionId, project_id: projectId },
    });
    if (!version) throw new Error('Version not found');

    version.version_prompt = await this.messagesService.generateVersionPrompt(projectId, versionId);
    await version.save();

    //TODO :: run action to generate code by prompt using all previous versions

    return version;
  }
}
