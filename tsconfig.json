{"compilerOptions": {"typeRoots": ["./src/types", "./node_modules/@types"], "module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "target": "ES2023", "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strictNullChecks": true, "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitAny": true, "strictBindCallApply": false, "noFallthroughCasesInSwitch": false}}