import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { APP_GUARD } from '@nestjs/core';
import { SequelizeModule } from '@nestjs/sequelize';

import { ClerkAuthGuard } from './common/guards/clerk-auth.guard';
import { clerkClientProviders } from './common/providers/clerk-client.providers';
import { getSequelizeConfig } from './database/sequelize.config';
import { MessagesModule } from './modules/messages/messages.module';
import { ProjectsModule } from './modules/projects/projects.module';
import { StatusModule } from './modules/status/status.module';
import { UsersModule } from './modules/users/users.module';

@Module({
  imports: [
    ConfigModule.forRoot({ isGlobal: true }),
    SequelizeModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useFactory: getSequelizeConfig,
    }),
    StatusModule,
    UsersModule,
    ProjectsModule,
    MessagesModule,
  ],
  providers: [
    ...clerkClientProviders,
    {
      provide: APP_GUARD,
      useClass: ClerkAuthGuard,
    },
  ],
})
export class AppModule {}
