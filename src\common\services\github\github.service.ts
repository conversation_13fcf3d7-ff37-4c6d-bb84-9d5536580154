import { Injectable } from '@nestjs/common';
import { Octokit } from '@octokit/rest';

@Injectable()
export class GithubService {
  private readonly octokit = new Octokit({
    auth: process.env.GITHUB_TOKEN,
  });

  async createRepository(name: string, isPrivate = false) {
    try {
      const response = await this.octokit.repos.createForAuthenticatedUser({
        name,
        private: isPrivate,
        auto_init: true,
      });
      return response.data;
    } catch (error) {
      console.error('Failed to create repository:', error);
      throw new Error('Failed to create repository');
    }
  }

  async createBranch(owner: string, repo: string, newBranch: string, baseBranch = 'main') {
    try {
      const { data: refData } = await this.octokit.git.getRef({
        owner,
        repo,
        ref: `heads/${baseBranch}`,
      });

      const baseSha = refData.object.sha;

      const { data: newRef } = await this.octokit.git.createRef({
        owner,
        repo,
        ref: `refs/heads/${newBranch}`,
        sha: baseSha,
      });

      return newRef;
    } catch (error) {
      console.error('Failed to create branch:', error);
      throw new Error('Failed to create branch');
    }
  }

  async deployToBranch(
    owner: string,
    repo: string,
    branch: string,
    filePath: string,
    content: string,
    commitMessage: string,
  ): Promise<any> {
    try {
      const encodedContent = Buffer.from(content).toString('base64');

      let sha: string | undefined;
      try {
        const { data } = await this.octokit.repos.getContent({
          owner,
          repo,
          path: filePath,
          ref: branch,
        });

        if (!Array.isArray(data)) {
          sha = data.sha;
        }
      } catch (error) {
        console.error('Failed to get branch content:', error);
      }

      const response = await this.octokit.repos.createOrUpdateFileContents({
        owner,
        repo,
        path: filePath,
        message: commitMessage,
        content: encodedContent,
        branch,
        sha,
      });

      return response.data;
    } catch (error) {
      console.error('Failed to deploy to branch:', error);
      throw new Error('Failed to deploy to branch');
    }
  }
}
