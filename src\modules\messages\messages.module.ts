import { forwardRef, <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';

import { Message } from './message.entity';
import { MessagesController } from './messages.controller';
import { MessagesService } from './messages.service';
import { ClaudeService } from '../../common/services/claude/claude.service';
import { ProjectUser } from '../projects/project-user.entity';
import { ProjectsModule } from '../projects/projects.module';

@Module({
  imports: [SequelizeModule.forFeature([Message, ProjectUser]), forwardRef(() => ProjectsModule)],
  providers: [MessagesService, ClaudeService],
  exports: [MessagesService],
  controllers: [MessagesController],
})
export class MessagesModule {}
